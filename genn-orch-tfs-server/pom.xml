<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.genn.app</groupId>
        <artifactId>genn-orch-tfs</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>genn-orch-tfs-server</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <esdk-obs-java.version>3.22.3.1</esdk-obs-java.version>
        <easyexcel.version>3.3.3</easyexcel.version>
        <aws-java-sdk-s3.version>1.12.429</aws-java-sdk-s3.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-event-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-xxl-job</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.genn.boot</groupId>
            <artifactId>genn-spring-boot-starter-cache</artifactId>
        </dependency>

        <!-- ======================= 其他三方依赖   =======================      -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.8.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 飞书SDK -->
        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
            <version>2.4.12</version>
            <optional>true</optional>
        </dependency>


    </dependencies>

    <build>
        <finalName>genn-orch-tfs</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!--  自身和外部api包管理,每次依赖一个新的外部api,在这里定义版本号,dev和test保持 为 genn-service-api.version     -->
        <profile>
            <id>local</id>
            <properties>
                <genn-trans-pms-api.version>1.0.0-SNAPSHOT</genn-trans-pms-api.version>
            </properties>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <genn-trans-pms-api.version>1.2.0-RELEASE</genn-trans-pms-api.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <genn-trans-pms-api.version>${genn-service-api.version}</genn-trans-pms-api.version>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <genn-trans-pms-api.version>${genn-service-api.version}</genn-trans-pms-api.version>
            </properties>
        </profile>
    </profiles>

</project>