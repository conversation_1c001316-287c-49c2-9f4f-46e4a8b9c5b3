package cn.genn.orch.tfs.application.service;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.orch.tfs.application.client.FeishuAppClient;
import cn.genn.orch.tfs.application.client.FeishuAppHelper;
import cn.genn.orch.tfs.application.command.FillSaveCommand;
import cn.genn.orch.tfs.application.dto.*;
import cn.genn.orch.tfs.application.query.DropDataQuery;
import cn.genn.orch.tfs.infrastructure.constant.CacheConstants;
import cn.genn.orch.tfs.infrastructure.exception.MessageCode;
import cn.genn.orch.tfs.infrastructure.properties.TfsProperties;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.service.bitable.v1.model.AppTableRecord;
import com.lark.oapi.service.bitable.v1.model.FilterInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FsWikService {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private TfsProperties tfsProperties;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER2 = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER3 = DateTimeFormatter.ofPattern("MM.dd");

    public FIllDTO queryFill(DropDataQuery query) {
        FIllDTO fillDTO = new FIllDTO(query);
        CompletableFuture<Integer> weekTimeFuture = CompletableFuture.supplyAsync(() -> feishuAppClient.getOverTimeCache(Collections.singletonList(query.getOpenId()), query.getStartTime(), query.getEndTime()).get(query.getOpenId()))
                .exceptionally(ex -> {
                    log.error("获取周总工时异常", ex);
                    return 0;
                });
        CompletableFuture<List<DropDataDTO>> dropDataListFuture = CompletableFuture.supplyAsync(() -> getUserDropDataList(query))
                .exceptionally(ex -> {
                    log.error("处理下拉框数据异常", ex);
                    return Collections.emptyList();
                });
        CompletableFuture<List<FillValueDTO>> fillValuesFuture = CompletableFuture.supplyAsync(() -> getFillValuesList(query))
                .exceptionally(ex -> {
                    log.error("处理已填报数据异常", ex);
                    return Collections.emptyList();
                });
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(weekTimeFuture, dropDataListFuture, fillValuesFuture);
        try {
            allFutures.join();
            fillDTO.setWeekTime((weekTimeFuture.get()+7)/8);
            List<DropDataDTO> dropDataDTOS = dropDataListFuture.get();
            fillDTO.setDropDataList(dropDataDTOS);
            List<FillValueDTO> fillValueDTOS = fillValuesFuture.get();
            fillValueDTOS = this.filterOldData(fillValueDTOS,dropDataDTOS);
            fillDTO.setFillValues(fillValueDTOS);
        } catch (Exception e) {
            log.error("异步任务执行失败", e);
            throw new BusinessException(MessageCode.QUERY_FILL_DATA_ERROR);
        }
        return fillDTO;
    }

    /**
     * 获取用户下拉框数据
     * @param query
     * @return
     */
    private List<DropDataDTO> getUserDropDataList(DropDataQuery query) {
        List<WikProjectDTO> dropDataList = this.getDropDataList();
        Map<String, DropDataDTO> resultMap = new HashMap<>();
        for (WikProjectDTO wikProjectDTO : dropDataList) {
            List<FsUser> users = wikProjectDTO.getOpenIds();
            List<String> openIds = users.stream().map(FsUser::getOpenId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(users) && openIds.contains(query.getOpenId())) {
                DropDataDTO dropDataDTO = resultMap.get(wikProjectDTO.getProjectName());
                if (dropDataDTO == null) {
                    dropDataDTO = new DropDataDTO();
                    dropDataDTO.setName(wikProjectDTO.getProjectName());
                    dropDataDTO.setCode(wikProjectDTO.getProjectName());
                    resultMap.put(wikProjectDTO.getProjectName(), dropDataDTO);
                }
                DropDataDTO children = new DropDataDTO();
                children.setName(wikProjectDTO.getDemandName());
                children.setCode(wikProjectDTO.getDemandName());
                dropDataDTO.getChildren().add(children);
            }
        }
        return resultMap.values().stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取多维表格数据
     */
    private List<WikProjectDTO> getDropDataList() {
        String wikProjectJson = stringRedisTemplate.opsForValue().get(CacheConstants.DROP_DATA_KEY);
        if (StrUtil.isNotEmpty(wikProjectJson)) {
            return JsonUtils.parseToList(wikProjectJson, WikProjectDTO.class);
        }
        List<WikProjectDTO> dropDataList = new ArrayList<>();
        //获取数据
        String wikToken = tfsProperties.getWik().getQueryWikToken();
        String wikTableId = tfsProperties.getWik().getQueryWikTableId();
        FilterInfo wikFilter = FeishuAppHelper.getQueryWikFilter();
        List<AppTableRecord> wikRecordList = feishuAppClient.getWikRecordList(wikToken, wikTableId, wikFilter);

        String oneWayWikToken = tfsProperties.getWik().getOneWayWikToken();
//        String oneWayWikTableId = tfsProperties.getWik().getOneWayWikTableId();
//        String appToken = feishuAppClient.getWikTableData(oneWayWikToken, null);
//        log.info("queryWik:{}",JsonUtils.toJson(wikRecordList));
        //处理数据
        for (AppTableRecord appTableRecord : wikRecordList) {
            WikProjectDTO dto = new WikProjectDTO();
            Map<String, Object> recordMap = appTableRecord.getFields();
            Object row1 = recordMap.get("所属业务线");
            if(ObjUtil.isNull(row1)){
                continue;
            }
            dto.setProjectName(FeishuAppHelper.getDrop(row1));
            Object row2 = recordMap.get("需求描述");
            if(ObjUtil.isNull(row2)){
                continue;
            }
            dto.setDemandName(FeishuAppHelper.getDrop(row2));
            List<FsUser> openIds = FeishuAppHelper.getUser(recordMap.get("工时填报人员"));
            if(CollUtil.isNotEmpty(openIds)){
                dto.setOpenIds(openIds);
                dropDataList.add(dto);
            }

        }
        if(CollUtil.isNotEmpty(dropDataList)){
            stringRedisTemplate.opsForValue().set(CacheConstants.DROP_DATA_KEY, JsonUtils.toJson(dropDataList), 60, TimeUnit.SECONDS);
        }
        return dropDataList;
    }

    /**
     * 获取已填报数据
     * @param query
     * @return
     */
    private List<FillValueDTO> getFillValuesList(DropDataQuery query) {
        String wikToken = tfsProperties.getWik().getSaveWikToken();
        String wikTableId = tfsProperties.getWik().getSaveWikTableId();
        int weekOfYear = query.getEndTime().get(java.time.temporal.WeekFields.ISO.weekOfYear());
        String time = weekOfYear+"周 "+DATE_TIME_FORMATTER3.format(query.getStartTime()) + "~" + DATE_TIME_FORMATTER3.format(query.getEndTime());
        FilterInfo wikFilter = FeishuAppHelper.getSaveWikFilter(time);
        List<AppTableRecord> wikRecordList = feishuAppClient.getWikRecordList(wikToken, wikTableId, wikFilter);
        //处理数据
        String oneWayWikToken = tfsProperties.getWik().getOneWayWikToken();
//        String oneWayWikTableId = tfsProperties.getWik().getOneWayWikTableId();
//        String appToken = feishuAppClient.getWikTableData(oneWayWikToken, null);
        List<FillValueDTO> fillValues = new ArrayList<>();
        for (AppTableRecord appTableRecord : wikRecordList) {
            Map<String, Object> recordMap = appTableRecord.getFields();
            List<FsUser> users = FeishuAppHelper.getUser(recordMap.get("姓名"));
            if (CollUtil.isEmpty(users)) {
                continue;
            }
            List<String> openIds = users.stream().map(FsUser::getOpenId).distinct().collect(Collectors.toList());
            if (!openIds.contains(query.getOpenId())) {
                continue;
            }
            FillValueDTO dto = new FillValueDTO();
            dto.setRecordId(appTableRecord.getRecordId());
            double workHour = FeishuAppHelper.getDouble(recordMap.get("投入人天（最小单位0.5人天）"));
            dto.setWorkHour(workHour);
            DropDataDTO dropData = new DropDataDTO();
            Object row1 = recordMap.get("业务线");
            if(ObjUtil.isNull(row1)){
                continue;
            }
            String projectName = FeishuAppHelper.getDrop(row1);
            dropData.setName(projectName);
            dropData.setCode(projectName);
            Object row2 = recordMap.get("关联需求");
            if(ObjUtil.isNull(row2)){
                continue;
            }
            String demandName = FeishuAppHelper.getDrop(row2);
            DropDataDTO dropDataChildren = new DropDataDTO();
            dropDataChildren.setName(demandName);
            dropDataChildren.setCode(demandName);
            dropData.setChildren(Collections.singletonList(dropDataChildren));
            dto.setDropData(dropData);
            fillValues.add(dto);
        }
        return fillValues;
    }

    /**
     * 过滤旧数据
     * @param fillValues
     * @param dropDataDTOS
     * @return
     */
    private List<FillValueDTO> filterOldData(List<FillValueDTO> fillValues,List<DropDataDTO> dropDataDTOS){
        if(CollUtil.isEmpty(fillValues)){
            return fillValues;
        }
        Map<String, DropDataDTO> dropDataDTOMap= dropDataDTOS.stream().collect(Collectors.toMap(DropDataDTO::getCode, Function.identity()));
        List<FillValueDTO> newFillValues = new ArrayList<>();
        for (FillValueDTO fillValue : fillValues) {
            DropDataDTO fillDrop = fillValue.getDropData();
            DropDataDTO dropDataDTO = dropDataDTOMap.get(fillDrop.getCode());
            if(ObjUtil.isNotNull(dropDataDTO)){
                List<String> childrenList = dropDataDTO.getChildren().stream().map(DropDataDTO::getCode).collect(Collectors.toList());
                if(childrenList.contains(fillDrop.getChildren().get(0).getCode())){
                    newFillValues.add(fillValue);
                }
            }
        }
        return newFillValues;

    }

    /**
     * 保存填报数据
     * @param command
     * @return
     */
    public Boolean save(FillSaveCommand command) {

        String wikToken = tfsProperties.getWik().getSaveWikToken();
        String wikTableId = tfsProperties.getWik().getSaveWikTableId();
        String appToken = feishuAppClient.getWikTableData(wikToken, null);
        //删除
        CompletableFuture.runAsync(()->{
            List<String> dropDataList = this.getDropDataList(command.getStartTime(), command.getEndTime(),command.getOpenId());
            if(CollUtil.isNotEmpty(dropDataList)){
                log.info("删除旧数据,command:{},删除数据dropDataList:{}",JsonUtils.toJson(command), JsonUtils.toJson(dropDataList));
                feishuAppClient.removeWikRecord(appToken, wikTableId, dropDataList);
            }
        }).exceptionally(ex -> {
            log.error("保存填报数据,删除旧数据异常", ex);
            return null;
        });

        String time = DATE_TIME_FORMATTER2.format(command.getStartTime()) + "~" + DATE_TIME_FORMATTER2.format(command.getEndTime());
        //添加新数据
        List<HashMap<String, Object>> fieldList = new ArrayList<>();
        for (FillValueDTO fillValue : command.getFillValues()) {
            HashMap<String, Object> fields = new HashMap<>();
            fields.put("业务线", fillValue.getDropData().getName());
            fields.put("关联需求", fillValue.getDropData().getChildren().get(0).getCode());
            fields.put("对应周", time);
            fields.put("姓名", Collections.singleton(new UserRecordDTO(command.getOpenId())));
            fields.put("投入人天（最小单位0.5人天）", fillValue.getWorkHour());
            fieldList.add(fields);
        }
        log.info("fieldList:{}",JsonUtils.toJson(fieldList));
        if(CollUtil.isNotEmpty(fieldList)){
            feishuAppClient.addBatchWikRecord(appToken, wikTableId, fieldList);
        }
        return true;
    }

    /**
     * 获取已存在的recordIds
     * @return
     */
    private List<String> getDropDataList(LocalDate startTime, LocalDate endTime,String openId) {
        String wikToken = tfsProperties.getWik().getSaveWikToken();
        String wikTableId = tfsProperties.getWik().getSaveWikTableId();
        int weekOfYear = endTime.get(java.time.temporal.WeekFields.ISO.weekOfYear());
        String time = weekOfYear+"周 "+DATE_TIME_FORMATTER3.format(startTime) + "~" + DATE_TIME_FORMATTER3.format(endTime);
        FilterInfo wikFilter = FeishuAppHelper.getSaveWikFilter(time);
        List<AppTableRecord> wikRecordList = feishuAppClient.getWikRecordList(wikToken, wikTableId, wikFilter);
        List<String> recordIds = new ArrayList<>();
        for (AppTableRecord appTableRecord : wikRecordList) {
            Map<String, Object> recordMap = appTableRecord.getFields();
            List<FsUser> users = FeishuAppHelper.getUser(recordMap.get("姓名"));
            if (CollUtil.isEmpty(users)) {
                continue;
            }
            List<String> openIds = users.stream().map(FsUser::getOpenId).distinct().collect(Collectors.toList());
            if (openIds.contains(openId)) {
                recordIds.add(appTableRecord.getRecordId());
            }
        }
        return recordIds;
    }


    /**
     * 单向关联
     *
     * @param project
     * @return
     */
    public List<OneWayAssociation> getOneWayAssociation(Object project,String appToken,String oneWayWikTableId) {
        if (project == null) {
            return null;
        }
        List<OneWayAssociation> result = new ArrayList<>();
        FsOneWayData fsOneWayData = JsonUtils.parse(JsonUtils.toJson(project), FsOneWayData.class);
        if(ObjUtil.isNull(fsOneWayData) || CollUtil.isEmpty(fsOneWayData.getLinkRecordIds())){
            return null;
        }
        List<AppTableRecord> wikRecordList = feishuAppClient.getWikRecordList(appToken, oneWayWikTableId, fsOneWayData.getLinkRecordIds());
        for (AppTableRecord appTableRecord : wikRecordList) {
            OneWayAssociation oneWay = new OneWayAssociation();
            oneWay.setRecordId(appTableRecord.getRecordId());
            String row1 = FeishuAppHelper.getText(appTableRecord.getFields().get("需求描述"));
            if(StrUtil.isEmpty(row1)){
                continue;
            }
            oneWay.setName(row1);
            result.add(oneWay);
        }
        return result;
    }
}
