package cn.genn.orch.tfs.interfaces;

import cn.genn.orch.tfs.application.command.FillSaveCommand;
import cn.genn.orch.tfs.application.dto.FIllDTO;
import cn.genn.orch.tfs.application.query.DropDataQuery;
import cn.genn.orch.tfs.application.service.FsWikService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 工时填报
 */
@Api(tags = "工时填报")
@Slf4j
@RestController
@RequestMapping("/fill")
public class FillController {

    @Resource
    private FsWikService fsWikService;

    @PostMapping("/queryFill")
    @ApiOperation(value = "查询填报页内容")
    public FIllDTO queryFill(@RequestBody @Validated DropDataQuery query){
        return fsWikService.queryFill(query);
    }

    @PostMapping("/save")
    @ApiOperation(value = "写入填报内容")
    public Boolean save(@RequestBody @Validated FillSaveCommand command){
        return fsWikService.save(command);
    }



}
