package cn.genn.orch.tfs.infrastructure.constant;

/**
 * 缓存相关key定义
 *
 * <AUTHOR>
 */
public class CacheConstants {

    public static final String CACHE_PRE_DEFAULT = "GENN:CACHE:";

    /**
     * 统一的缓存前缀
     */
    public static final String CACHE_PRE = "GENN:TFS:CORE";
    public static final String LOCK_PRE = "GENN:TFS:LOCK";

    public static final String DROP_DATA_KEY = CACHE_PRE + ":DROP_DATA";

    public static final String USER_OVER_TIME = ":USER_OVER_TIME:";

    public static final String HOLIDAY = ":HOLIDAY:";

    public static final String WIK_TOKEN = ":WIK_TOKEN:";


    public static String getCacheUserOverTime(String openId) {
        return CACHE_PRE + USER_OVER_TIME + openId;
    }

    public static String getCacheHoliday(String yearMonth){
        return CACHE_PRE + HOLIDAY + yearMonth;
    }

    public static String getCacheWikAppToken(String wikToken) {
        return CACHE_PRE + WIK_TOKEN + wikToken;
    }

}
