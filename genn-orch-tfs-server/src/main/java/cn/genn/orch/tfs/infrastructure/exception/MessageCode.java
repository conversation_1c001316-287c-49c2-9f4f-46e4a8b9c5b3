package cn.genn.orch.tfs.infrastructure.exception;

import cn.genn.core.exception.MessageCodeWrap;

/**
 * 业务错误码定义
 * 从201-899
 *
 * <AUTHOR>
 */
public enum MessageCode implements MessageCodeWrap {

    RESOURCE_NOT_EXIST("201", "资源不存在"),
    //飞书调用相关
    FEISHU_GET_WIK_FAIL("301", "获取飞书多维表格节点信息失败"),
    FEISHU_GET_WIK_DATE_FAIL("302", "获取飞书多维表格数据失败"),
    QUERY_FILL_DATA_ERROR("303", "查询填报数据异常"),
    CALENDAR_DATA_ERROR("304", "获取飞书日历数据异常"),
    OPEN_DATA_ERROR("305", "获取人员加班请假数据异常"),
    SEND_MESSAGE_FAIL("306", "发送飞书卡片消息失败"),
    WIK_ADD_RECORD_FAIL("307", "多维表格添加数据失败"),
    WIK_UPDATE_RECORD_FAIL("308", "多维表格编辑数据失败"),
    WIK_DELETE_RECORD_FAIL("309", "多维表格删除数据失败"),
    TELEPHONE_NOT_EXIST("310","手机号所属用户不存在"),
    GET_OPEN_ID_FAIL("311","获取用户openId失败"),
    USER_INFO_FAIL("312","获取飞书用户信息失败"),
    CHILDREN_DEPARTMENT_INFO_FAIL("313","获取子部门信息失败"),
    CONTACT_INFO_FAIL("314","获取企业通讯录范围失败"),
    ;

    private final String code;
    private final String description;

    MessageCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
